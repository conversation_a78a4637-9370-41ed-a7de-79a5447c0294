import { Feather, Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import {
    Alert,
    Keyboard,
    Platform,
    SafeAreaView,
    StatusBar,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';

const OTP_LENGTH = 4;

export default function VerifyOtpScreen() {
  const params = useLocalSearchParams();
  const phoneNumber = params.phoneNumber as string || 'your phone number';

  const [otp, setOtp] = useState<string[]>(new Array(OTP_LENGTH).fill(''));
  const [isOtpComplete, setIsOtpComplete] = useState(false);
  const [resendDisabled, setResendDisabled] = useState(true);
  const [countdown, setCountdown] = useState(30); // 30 seconds countdown

  const inputRefs = useRef<(TextInput | null)[]>([]);

  useEffect(() => {
    // Start countdown for resend button
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    } else {
      setResendDisabled(false);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  useEffect(() => {
    setIsOtpComplete(otp.every(digit => digit !== '') && otp.join('').length === OTP_LENGTH);
  }, [otp]);

  const handleOtpChange = (text: string, index: number) => {
    const newOtp = [...otp];
    newOtp[index] = text;
    setOtp(newOtp);

    // Move to next input or submit
    if (text && index < OTP_LENGTH - 1) {
      inputRefs.current[index + 1]?.focus();
    } else if (!text && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }

    if (newOtp.every(digit => digit !== '') && newOtp.join('').length === OTP_LENGTH) {
      Keyboard.dismiss();
      // Potentially auto-submit here or wait for button press
    }
  };

  const handleVerifyOtp = () => {
    const enteredOtp = otp.join('');
    // TODO: Implement OTP verification logic
    console.log('Verifying OTP:', enteredOtp, 'for number:', phoneNumber);
    // Simulate API call
    if (enteredOtp === '1234') { // Replace with actual verification
      Alert.alert('Success', 'OTP Verified Successfully!');
      router.replace('/(tabs)'); // Navigate to main app
    } else {
      Alert.alert('Error', 'Invalid OTP. Please try again.');
      setOtp(new Array(OTP_LENGTH).fill('')); // Clear OTP fields
      inputRefs.current[0]?.focus(); // Focus on the first input
    }
  };

  const handleResendCode = () => {
    if (resendDisabled) return;
    // TODO: Implement resend OTP logic
    console.log('Resending OTP to:', phoneNumber);
    setResendDisabled(true);
    setCountdown(30); // Restart countdown
    setOtp(new Array(OTP_LENGTH).fill('')); // Clear OTP fields
    inputRefs.current[0]?.focus(); // Focus on the first input
    Alert.alert('OTP Resent', 'A new OTP has been sent to your number.');
  };

  const handleBack = () => {
    if (router.canGoBack()) {
      router.back();
    } else {
      router.replace('/enterNumber');
    }
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle={Platform.OS === 'ios' ? 'dark-content' : 'default'} />
      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <Ionicons name="chevron-back" size={28} color="#333333" />
        </TouchableOpacity>
      </View>

      <View style={styles.contentContainer}>
        <Text style={styles.title}>Enter your {OTP_LENGTH}-digit code</Text>
        <Text style={styles.subtitle}>
          Code sent to {phoneNumber}. 
        </Text>

        <View style={styles.otpInputContainer}>
          {otp.map((digit, index) => (
            <TextInput
              key={index}
              ref={ref => inputRefs.current[index] = ref}
              style={styles.otpInput}
              keyboardType="number-pad"
              maxLength={1}
              onChangeText={(text) => handleOtpChange(text, index)}
              value={digit}
              onKeyPress={({ nativeEvent: { key: keyValue } }) => {
                if (keyValue === 'Backspace' && !otp[index] && index > 0) {
                  inputRefs.current[index - 1]?.focus();
                }
              }}
              autoFocus={index === 0} // Auto-focus on the first input
            />
          ))}
        </View>

        <TouchableOpacity onPress={handleResendCode} disabled={resendDisabled}>
          <Text style={[styles.resendText, resendDisabled && styles.resendTextDisabled]}>
            Resend Code {resendDisabled ? `(${countdown}s)` : ''}
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.footerContainer}>
        <TouchableOpacity 
          style={[styles.nextButton, !isOtpComplete && styles.nextButtonDisabled]}
          onPress={handleVerifyOtp}
          disabled={!isOtpComplete}
        >
          <Feather name="arrow-right" size={24} color="white" />
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#F7F7F7',
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingTop: Platform.OS === 'ios' ? 10 : 20,
    paddingBottom: 10,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  backButton: {
    padding: 5,
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 25,
    paddingTop: 30,
    alignItems: 'center', // Center content horizontally
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 10,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 22,
  },
  otpInputContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '80%', // Adjust width as needed
    marginBottom: 30,
  },
  otpInput: {
    width: 50,
    height: 55,
    borderWidth: 1,
    borderColor: '#D0D0D0',
    borderRadius: 8,
    textAlign: 'center',
    fontSize: 22,
    fontWeight: '600',
    color: '#333333',
    backgroundColor: '#FFFFFF',
  },
  resendText: {
    fontSize: 16,
    color: '#50C2C9', // Teal color
    fontWeight: '500',
    marginBottom: 30,
  },
  resendTextDisabled: {
    color: '#A0A0A0', // Grayed out when disabled
  },
  footerContainer: {
    padding: 25,
    alignItems: 'flex-end',
    backgroundColor: '#F7F7F7',
  },
  nextButton: {
    backgroundColor: '#50C2C9',
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.23,
    shadowRadius: 2.62,
    elevation: 4,
  },
  nextButtonDisabled: {
    backgroundColor: '#A0D8DC',
  },
});