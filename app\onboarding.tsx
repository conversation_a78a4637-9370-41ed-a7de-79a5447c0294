import { router } from 'expo-router';
import React from 'react';
import {
    ImageBackground,
    SafeAreaView,
    StatusBar,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

// تأكد من إضافة الصورة 'onboarding-background.png' إلى مجلد 'assets/images'
// يمكنك تغيير اسم الملف إذا لزم الأمر
const onboardingImage = require('@/assets/images/onboarding-background.png'); 

export default function OnboardingScreen() {
  const handleGetStarted = () => {
    router.replace('/signIn'); // Navigate to the Sign In screen
  };

  return (
    <ImageBackground source={onboardingImage} style={styles.backgroundImage}>
      <StatusBar barStyle="light-content" />
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.contentContainer}>
          <Text style={styles.title}>Welcome to our store</Text>
          <Text style={styles.subtitle}>
            Discover the beauty of the Egyptian desert through authentic products inspired by its rich heritage.
          </Text>
          <TouchableOpacity style={styles.button} onPress={handleGetStarted} activeOpacity={0.8}>
            <Text style={styles.buttonText}>Get Started</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </ImageBackground>
  );
}

const styles = StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },
  safeArea: {
    flex: 1,
    justifyContent: 'flex-end', // Aligns content to the bottom
  },
  contentContainer: {
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 50, // Adjust as needed for spacing from bottom
  },
  title: {
    fontSize: 30, // Adjusted for better fit
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 12,
    // fontFamily: 'YourCustomFont-Bold', // إذا كنت تستخدم خطًا مخصصًا
  },
  subtitle: {
    fontSize: 16,
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 40,
    lineHeight: 22, // For better readability
    // fontFamily: 'YourCustomFont-Regular', // إذا كنت تستخدم خطًا مخصصًا
  },
  button: {
    backgroundColor: '#50C2C9', // Approximate color from the image
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 30, // Rounded corners
    width: '90%',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600', // Slightly less bold than title
    // fontFamily: 'YourCustomFont-SemiBold', // إذا كنت تستخدم خطًا مخصصًا
  },
});