import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import {
    Alert,
    FlatList,
    Image,
    Platform,
    SafeAreaView,
    ScrollView,
    StatusBar,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';

// Mock data - in a real app, this would come from global state management
const mockCartItems = [
  {
    id: 'rm1',
    name: 'Rice Milk Sakalans',
    price: 16,
    quantity: 3,
    image: require('@/assets/images/placeholder-item.svg'),
  },
  {
    id: 'konbla',
    name: '<PERSON>n<PERSON>',
    description: 'Lebanese Meal',
    price: 17,
    quantity: 1,
    image: require('@/assets/images/placeholder-item.svg'),
  },
];

const mockSuggestedItems = [
  {
    id: 'rm2',
    name: 'Rice Milk Sakalans',
    price: 16,
    image: require('@/assets/images/placeholder-item.svg'),
  },
  {
    id: 'konbla2',
    name: '<PERSON>n<PERSON>',
    price: 17,
    image: require('@/assets/images/placeholder-item.svg'),
  },
];

interface CartItemProps {
  item: {
    id: string;
    name: string;
    price: number;
    quantity: number;
    image: any;
  };
  onQuantityChange: (id: string, newQuantity: number) => void;
  onRemove: (id: string) => void;
}

const CartItem: React.FC<CartItemProps> = ({ item, onQuantityChange, onRemove }) => {
  const handleQuantityChange = (change: number) => {
    const newQuantity = item.quantity + change;
    if (newQuantity >= 1) {
      onQuantityChange(item.id, newQuantity);
    } else {
      onRemove(item.id);
    }
  };

  return (
    <View style={styles.cartItemContainer}>
      <Image source={item.image} style={styles.itemImage} />
      <View style={styles.itemDetailsContainer}>
        <Text style={styles.itemName}>{item.name}</Text>
        <TouchableOpacity>
          <Text style={styles.editText}>Edit</Text>
        </TouchableOpacity>
        <Text style={styles.itemPrice}>AED{item.price}</Text>
      </View>
      <View style={styles.quantityContainer}>
        <TouchableOpacity
          style={styles.quantityButton}
          onPress={() => handleQuantityChange(-1)}
        >
          <Ionicons name="remove" size={16} color="#333" />
        </TouchableOpacity>

        <Text style={styles.quantityText}>{item.quantity}</Text>

        <TouchableOpacity
          style={styles.quantityButton}
          onPress={() => handleQuantityChange(1)}
        >
          <Ionicons name="add" size={16} color="#333" />
        </TouchableOpacity>
      </View>
    </View>
  );
};

interface SuggestedItemProps {
  item: {
    id: string;
    name: string;
    price: number;
    image: any;
  };
  onAdd: (item: any) => void;
}

const SuggestedItem: React.FC<SuggestedItemProps> = ({ item, onAdd }) => {
  return (
    <View style={styles.suggestedItemContainer}>
      <Image source={item.image} style={styles.suggestedItemImage} />
      <Text style={styles.suggestedItemName}>{item.name}</Text>
      <Text style={styles.suggestedItemPrice}>AED{item.price}</Text>
      <TouchableOpacity style={styles.addButton} onPress={() => onAdd(item)}>
        <Ionicons name="add" size={20} color="#50C2C9" />
      </TouchableOpacity>
    </View>
  );
};

export default function CheckoutScreen() {
  const router = useRouter();
  const { state, updateQuantity, removeItem, addItem, clearCart } = useCart();
  const [promoCode, setPromoCode] = useState('');
  const [isPromoApplied, setIsPromoApplied] = useState(false);

  const cartItems = state.items;

  const handleGoBack = () => {
    router.back();
  };

  const handleQuantityChange = (id: string, newQuantity: number) => {
    updateQuantity(id, newQuantity);
  };

  const handleRemoveItem = (id: string) => {
    removeItem(id);
  };

  const handleAddSuggestedItem = (suggestedItem: any) => {
    addItem(suggestedItem);
  };

  const handleApplyPromo = () => {
    if (promoCode.trim()) {
      setIsPromoApplied(true);
      // In a real app, you would validate the promo code with an API
    }
  };

  const handleContinueToCheckout = () => {
    Alert.alert(
      'Order Placed!',
      'Your order has been successfully placed. You will receive a confirmation shortly.',
      [
        {
          text: 'OK',
          onPress: () => {
            clearCart(); // Clear the cart after successful order
            router.push('/(tabs)'); // Navigate back to home
          },
        },
      ]
    );
  };

  // Calculate totals
  const subtotal = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const couponDiscount = isPromoApplied ? 14 : 0;
  const deliveryFee = 8;
  const total = subtotal - couponDiscount + deliveryFee;
  const savings = isPromoApplied ? 14 : 0;

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle={Platform.OS === 'ios' ? 'dark-content' : 'default'} />

      {/* Header */}
      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={handleGoBack} style={styles.backButton}>
          <Ionicons name="chevron-back" size={28} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>My Cart</Text>
        <View style={styles.headerPlaceholder} />
      </View>

      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        {/* Cart Items */}
        <View style={styles.cartItemsSection}>
          {cartItems.map((item) => (
            <CartItem
              key={item.id}
              item={item}
              onQuantityChange={handleQuantityChange}
              onRemove={handleRemoveItem}
            />
          ))}
        </View>

        {/* You might also like section */}
        <View style={styles.suggestedSection}>
          <Text style={styles.sectionTitle}>You might also like</Text>
          <FlatList
            data={mockSuggestedItems}
            renderItem={({ item }) => (
              <SuggestedItem item={item} onAdd={handleAddSuggestedItem} />
            )}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.suggestedItemsList}
          />
        </View>

        {/* Promo Code Section */}
        <View style={styles.promoSection}>
          <Text style={styles.sectionTitle}>Promo code</Text>
          <View style={styles.promoInputContainer}>
            <TextInput
              style={styles.promoInput}
              placeholder="Add your promo code"
              placeholderTextColor="#999"
              value={promoCode}
              onChangeText={setPromoCode}
            />
            <TouchableOpacity style={styles.applyButton} onPress={handleApplyPromo}>
              <Text style={styles.applyButtonText}>Apply</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>

      {/* Summary and Checkout */}
      <View style={styles.bottomSection}>
        {/* Summary */}
        <View style={styles.summaryContainer}>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Subtotal</Text>
            <Text style={styles.summaryValue}>AED{subtotal}</Text>
          </View>
          {isPromoApplied && (
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Coupon discount</Text>
              <Text style={styles.discountValue}>-AED{couponDiscount}</Text>
            </View>
          )}
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Delivery Fee</Text>
            <Text style={styles.summaryValue}>AED{deliveryFee}</Text>
          </View>
          <View style={[styles.summaryRow, styles.totalRow]}>
            <Text style={styles.totalLabel}>Total</Text>
            <Text style={styles.totalValue}>AED{total}</Text>
          </View>
        </View>

        {/* Savings indicator */}
        {savings > 0 && (
          <View style={styles.savingsContainer}>
            <Ionicons name="checkmark-circle" size={16} color="#4CAF50" />
            <Text style={styles.savingsText}>YOU SAVED AED{savings}</Text>
          </View>
        )}

        {/* Continue to checkout button */}
        <TouchableOpacity style={styles.checkoutButton} onPress={handleContinueToCheckout}>
          <Text style={styles.checkoutButtonText}>Continue to checkout</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#F8F8F8',
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  headerPlaceholder: {
    width: 30,
  },
  scrollContainer: {
    flex: 1,
  },
  cartItemsSection: {
    paddingHorizontal: 15,
    paddingTop: 15,
  },
  cartItemContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 15,
    marginBottom: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  itemImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginRight: 15,
    backgroundColor: '#EFEFEF',
  },
  itemDetailsContainer: {
    flex: 1,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  editText: {
    fontSize: 12,
    color: '#50C2C9',
    marginBottom: 8,
  },
  itemPrice: {
    fontSize: 15,
    fontWeight: 'bold',
    color: '#333',
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quantityButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F5F5F5',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  quantityText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginHorizontal: 15,
    minWidth: 20,
    textAlign: 'center',
  },
  suggestedSection: {
    paddingTop: 20,
    paddingBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    paddingHorizontal: 15,
    marginBottom: 15,
  },
  suggestedItemsList: {
    paddingHorizontal: 15,
  },
  suggestedItemContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 15,
    marginRight: 12,
    alignItems: 'center',
    width: 150,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  suggestedItemImage: {
    width: 80,
    height: 60,
    borderRadius: 8,
    marginBottom: 10,
    backgroundColor: '#EFEFEF',
  },
  suggestedItemName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
    marginBottom: 5,
  },
  suggestedItemPrice: {
    fontSize: 13,
    fontWeight: 'bold',
    color: '#50C2C9',
    marginBottom: 10,
  },
  addButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F0F9FA',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#50C2C9',
  },
  promoSection: {
    paddingHorizontal: 15,
    paddingBottom: 20,
  },
  promoInputContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    paddingHorizontal: 15,
    paddingVertical: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  promoInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  applyButton: {
    paddingHorizontal: 20,
    paddingVertical: 8,
  },
  applyButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#50C2C9',
  },
  bottomSection: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    paddingTop: 15,
    paddingBottom: 20,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  summaryContainer: {
    marginBottom: 15,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 14,
    color: '#666',
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  discountValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#D32F2F',
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
    paddingTop: 8,
    marginTop: 8,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  totalValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#50C2C9',
  },
  savingsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
  },
  savingsText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#4CAF50',
    marginLeft: 5,
  },
  checkoutButton: {
    backgroundColor: '#50C2C9',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkoutButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});