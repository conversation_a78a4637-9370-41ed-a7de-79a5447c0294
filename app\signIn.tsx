import { FontAwesome } from '@expo/vector-icons'; // Import FontAwesome
import { router } from 'expo-router'; // Import router for navigation
import React, { useState } from 'react';
import {
  ImageBackground,
  Platform,
  SafeAreaView,
  StatusBar,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';

// تأكد من إضافة الصورة 'sign-in-background.png' إلى مجلد 'assets/images'
// يمكنك تغيير اسم الملف إذا لزم الأمر
const signInBackgroundImage = require('@/assets/images/sign-in-background.png');

export default function SignInScreen() {
  const [phoneNumber, setPhoneNumber] = useState('');

  const handleContinueWithGoogle = () => {
    // TODO: Implement Google Sign-In
    console.log('Continue with Google pressed');
    // router.replace('/(tabs)'); // Example navigation after successful sign-in
  };

  const handleContinueWithFacebook = () => {
    // TODO: Implement Facebook Sign-In
    console.log('Continue with Facebook pressed');
    // router.replace('/(tabs)'); // Example navigation after successful sign-in
  };

  const handleSignInWithPhoneNumber = () => {
    // Navigate to the enter number screen
    router.push('/enterNumber');
  };

  return (
    <ImageBackground source={signInBackgroundImage} style={styles.backgroundImage}>
      <StatusBar barStyle="light-content" />
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.headerContainer}>
          {/* يمكن إضافة زر رجوع هنا إذا لزم الأمر */}
        </View>
        <View style={styles.contentContainer}>
          <Text style={styles.title}>Discover the magic of Egyptian sweets!</Text>

          <View style={styles.phoneInputContainer}>
            <Text style={styles.countryCodeFlag}>🇦🇪</Text>
            <Text style={styles.countryCode}>+971</Text>
            <TextInput
              style={styles.phoneInput}
              placeholder="Enter your phone number"
              placeholderTextColor="#A0A0A0" // Light gray placeholder
              keyboardType="phone-pad"
              value={phoneNumber}
              onChangeText={setPhoneNumber}
              onSubmitEditing={handleSignInWithPhoneNumber} // Optional: submit on keyboard done
            />
          </View>
          {/* يمكن إضافة زر لإرسال رقم الهاتف هنا إذا لم يكن الإرسال عبر لوحة المفاتيح كافيًا */}

          <Text style={styles.socialConnectText}>Or connect with social media</Text>

          <TouchableOpacity style={[styles.button, styles.googleButton]} onPress={handleContinueWithGoogle} activeOpacity={0.8}>
            <FontAwesome name="google" size={20} color="white" style={styles.iconStyle} />
            <Text style={styles.buttonText}>Continue with Google</Text>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.button, styles.facebookButton]} onPress={handleContinueWithFacebook} activeOpacity={0.8}>
            <FontAwesome name="facebook-square" size={20} color="white" style={styles.iconStyle} />
            <Text style={styles.buttonText}>Continue with Facebook</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </ImageBackground>
  );
}

const styles = StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },
  safeArea: {
    flex: 1,
  },
  headerContainer: {
    height: 60, // مساحة لرأس الصفحة، يمكن تعديلها
    justifyContent: 'center',
    paddingHorizontal: 15,
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center', // يركز المحتوى في المنتصف عمودياً
    alignItems: 'center',
    paddingHorizontal: 30,
    paddingBottom: Platform.OS === 'ios' ? 20 : 50, // مساحة إضافية في الأسفل
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333333', // Dark gray text
    textAlign: 'center',
    marginBottom: 40,
    // fontFamily: 'YourCustomFont-Bold',
  },
  phoneInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    paddingHorizontal: 15,
    paddingVertical: Platform.OS === 'ios' ? 15 : 10, // تعديل الحشو ليتناسب مع الأنظمة المختلفة
    marginBottom: 25,
    width: '100%',
    borderWidth: 1,
    borderColor: '#E0E0E0', // Light border color
  },
  countryCodeFlag: {
    fontSize: 24,
    marginRight: 8,
  },
  countryCode: {
    fontSize: 16,
    color: '#333333',
    marginRight: 8,
    // fontFamily: 'YourCustomFont-Medium',
  },
  phoneInput: {
    flex: 1,
    fontSize: 16,
    color: '#333333',
    // fontFamily: 'YourCustomFont-Regular',
  },
  iconStyle: {
    marginRight: 10,
  },
  socialConnectText: {
    fontSize: 14,
    color: '#808080', // Medium gray text
    textAlign: 'center',
    marginBottom: 25,
    // fontFamily: 'YourCustomFont-Regular',
  },
  button: {
    paddingVertical: 15,
    borderRadius: 10,
    width: '100%',
    alignItems: 'center',
    marginBottom: 15,
    flexDirection: 'row', // للسماح بوضع أيقونة بجانب النص
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.20,
    shadowRadius: 1.41,
    elevation: 2,
  },
  googleButton: {
    backgroundColor: '#4285F4', // Google blue
  },
  facebookButton: {
    backgroundColor: '#3b5998', // Facebook blue
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 10, // مسافة إذا كان هناك أيقونة
    // fontFamily: 'YourCustomFont-Medium',
  },
});