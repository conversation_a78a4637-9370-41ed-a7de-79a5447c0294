import { Ionicons } from '@expo/vector-icons';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useState } from 'react';
import {
    Image,
    Platform,
    SafeAreaView,
    ScrollView,
    StatusBar,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';

// Product data - in a real app, this would come from an API or database
const productData: { [key: string]: any } = {
  'rice-milk-caesar': {
    id: 'rm5',
    name: 'Rice Milk Caesar',
    description: 'Rice pudding with topping of mix fruits qeshta and basbosa',
    price: 17,
    image: require('@/assets/images/placeholder-item.svg'),
    category: 'Rice Milk',
    ingredients: ['Rice pudding', 'Mixed fruits', 'Qeshta', 'Basbosa'],
    nutritionInfo: {
      calories: 320,
      protein: '8g',
      carbs: '45g',
      fat: '12g'
    }
  },
  'rice-milk-sakalans': {
    id: 'rm1',
    name: 'Rice Milk Sakalans',
    description: 'Rice Milk with fruits',
    price: 16,
    image: require('@/assets/images/placeholder-item.svg'),
    category: 'Rice Milk',
  },
  'rice-milk-pistachio': {
    id: 'rm2',
    name: 'Rice Milk Pistachio',
    description: 'Rice Milk with pistachio sauce',
    price: 15,
    image: require('@/assets/images/placeholder-item.svg'),
    category: 'Rice Milk',
  },
  // Add more products as needed
};

export default function ProductDetailScreen() {
  const router = useRouter();
  const { productId } = useLocalSearchParams<{ productId: string }>();
  const [quantity, setQuantity] = useState(1);

  // Get product data
  const product = productId ? productData[productId] : null;

  if (!product) {
    return (
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Product not found</Text>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <Text style={styles.backButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  const handleGoBack = () => {
    router.back();
  };

  const handleQuantityChange = (change: number) => {
    const newQuantity = quantity + change;
    if (newQuantity >= 1) {
      setQuantity(newQuantity);
    }
  };

  const handleAddToBasket = () => {
    // TODO: Implement add to basket logic
    console.log(`Added ${quantity} x ${product.name} to basket`);
    // You could show a success message or navigate to cart
  };

  const totalPrice = product.price * quantity;

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle={Platform.OS === 'ios' ? 'dark-content' : 'default'} />

      {/* Header */}
      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={handleGoBack} style={styles.headerBackButton}>
          <Ionicons name="chevron-back" size={28} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{product.category}</Text>
        <View style={styles.headerPlaceholder} />
      </View>

      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        {/* Product Image */}
        <View style={styles.imageContainer}>
          <Image source={product.image} style={styles.productImage} />
        </View>

        {/* Product Info */}
        <View style={styles.productInfoContainer}>
          <Text style={styles.productName}>{product.name}</Text>
          <Text style={styles.productDescription}>{product.description}</Text>

          <View style={styles.priceContainer}>
            <Text style={styles.currencyLabel}>AED</Text>
            <Text style={styles.price}>{product.price}</Text>
          </View>

          {/* Quantity Selector */}
          <View style={styles.quantityContainer}>
            <TouchableOpacity
              style={styles.quantityButton}
              onPress={() => handleQuantityChange(-1)}
              disabled={quantity <= 1}
            >
              <Ionicons name="remove" size={20} color={quantity <= 1 ? "#ccc" : "#333"} />
            </TouchableOpacity>

            <Text style={styles.quantityText}>{quantity}</Text>

            <TouchableOpacity
              style={styles.quantityButton}
              onPress={() => handleQuantityChange(1)}
            >
              <Ionicons name="add" size={20} color="#333" />
            </TouchableOpacity>
          </View>

          {/* Add to Basket Button */}
          <TouchableOpacity style={styles.addToBasketButton} onPress={handleAddToBasket}>
            <Text style={styles.addToBasketText}>Add to basket</Text>
            <Text style={styles.addToBasketPrice}>AED {totalPrice}</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#F8F8F8',
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  headerBackButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  headerPlaceholder: {
    width: 30,
  },
  scrollContainer: {
    flex: 1,
  },
  imageContainer: {
    backgroundColor: '#FFFFFF',
    paddingVertical: 30,
    alignItems: 'center',
  },
  productImage: {
    width: 300,
    height: 200,
    borderRadius: 12,
    backgroundColor: '#EFEFEF',
    resizeMode: 'cover',
  },
  productInfoContainer: {
    backgroundColor: '#FFFFFF',
    marginTop: 20,
    paddingHorizontal: 20,
    paddingVertical: 25,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    flex: 1,
  },
  productName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  productDescription: {
    fontSize: 16,
    color: '#666',
    lineHeight: 22,
    marginBottom: 20,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 30,
  },
  currencyLabel: {
    fontSize: 16,
    color: '#50C2C9',
    fontWeight: '600',
    marginRight: 4,
  },
  price: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#50C2C9',
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 30,
  },
  quantityButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F5F5',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  quantityText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginHorizontal: 30,
    minWidth: 30,
    textAlign: 'center',
  },
  addToBasketButton: {
    backgroundColor: '#50C2C9',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  addToBasketText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  addToBasketPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: '#666',
    marginBottom: 20,
  },
  backButton: {
    backgroundColor: '#50C2C9',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  backButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
});
