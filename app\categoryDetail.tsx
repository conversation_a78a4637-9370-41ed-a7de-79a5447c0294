import { Ionicons } from '@expo/vector-icons';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React from 'react';
import { FlatList, Image, Platform, SafeAreaView, StatusBar, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

// Dummy data for items within a category - replace with actual data fetching
const categoryItemsData: { [key: string]: any[] } = {
  'Rice Milk': [
    { id: 'rm1', name: 'Rice Milk Sakalans', description: 'Rice Milk with fruits', price: 16, image: require('@/assets/images/placeholder-item.svg') },
    { id: 'rm2', name: 'Rice Milk Pistachio', description: 'Rice Milk with pistachio sauce', price: 15, image: require('@/assets/images/placeholder-item.svg') },
    { id: 'rm3', name: 'Rice Milk Mango', description: 'Rice Milk with Mango', price: 15, image: require('@/assets/images/placeholder-item.svg') },
    { id: 'rm4', name: 'Rice Milk Nutella', description: 'Rice Milk with Nutella', price: 13, image: require('@/assets/images/placeholder-item.svg') },
    { id: 'rm5', name: 'Rice Milk Caesar', description: 'Rice pudding with topping of mix fruits qeshta and basbosa', price: 17, image: require('@/assets/images/placeholder-item.svg') },
  ],
  // Add other categories and their items here
  'Cheesecake': [
    { id: 'cc1', name: 'Classic Cheesecake', description: 'Creamy classic cheesecake', price: 18, image: require('@/assets/images/placeholder-item.svg') },
  ],
  'Breakfast': [
    { id: 'bf1', name: 'Full Breakfast Platter', description: 'Eggs, bacon, toast, and beans', price: 25, image: require('@/assets/images/placeholder-item.svg') },
  ],
  'Farghaly Juice': [
    { id: 'fj1', name: 'Mango Farghaly', description: 'Fresh mango juice blend', price: 12, image: require('@/assets/images/placeholder-item.svg') },
  ],
  'Ashtoota': [
    { id: 'as1', name: 'Ashtoota Cream', description: 'Sweet ashtoota with cream', price: 14, image: require('@/assets/images/placeholder-item.svg') },
  ],
};

interface ItemCardProps {
  item: {
    id: string;
    name: string;
    description: string;
    price: number;
    image: any; // Adjust type as per your image source
  };
  onAddToCart: (item: any) => void;
  onItemPress: (item: any) => void;
}

const ItemCard: React.FC<ItemCardProps> = ({ item, onAddToCart, onItemPress }) => {
  return (
    <TouchableOpacity style={styles.itemCardContainer} onPress={() => onItemPress(item)}>
      <Image source={item.image} style={styles.itemImage} />
      <View style={styles.itemDetailsContainer}>
        <Text style={styles.itemName}>{item.name}</Text>
        <Text style={styles.itemDescription}>{item.description}</Text>
        <Text style={styles.itemPrice}>AED{item.price}</Text>
      </View>
      <TouchableOpacity
        style={styles.addButton}
        onPress={(e) => {
          e.stopPropagation();
          onAddToCart(item);
        }}
      >
        <Ionicons name="add" size={24} color="white" />
      </TouchableOpacity>
    </TouchableOpacity>
  );
};

export default function CategoryDetailScreen() {
  const router = useRouter();
  const { categoryName } = useLocalSearchParams<{ categoryName: string }>();

  // In a real app, you would fetch items based on categoryName or pass them as params
  const items = categoryName ? categoryItemsData[categoryName] || [] : [];

  const handleAddToCart = (item: any) => {
    // TODO: Implement add to cart logic
    console.log('Added to cart:', item.name);
    // Potentially update a global cart state or navigate to cart
  };

  const handleItemPress = (item: any) => {
    // Convert item name to a URL-friendly format for productId
    const productId = item.name.toLowerCase().replace(/\s+/g, '-');
    router.push({ pathname: '/productDetail', params: { productId } });
  };

  const handleGoBack = () => {
    router.back();
  };

  // Mock cart data - in a real app, this would come from global state
  const cartTotal = 25; // Total cart value
  const cartSubtotal = 17; // Subtotal without delivery
  const hasItemsInCart = cartTotal > 0;

  const handleViewCart = () => {
    router.push('/cart');
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle={Platform.OS === 'ios' ? 'dark-content' : 'default'} />
      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={handleGoBack} style={styles.backButton}>
          <Ionicons name="chevron-back" size={28} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{categoryName || 'Category'}</Text>
        <View style={styles.placeholder} />{/* To balance the header */}
      </View>

      <FlatList
        data={items}
        renderItem={({ item }) => (
          <ItemCard
            item={item}
            onAddToCart={handleAddToCart}
            onItemPress={handleItemPress}
          />
        )}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContentContainer}
        showsVerticalScrollIndicator={false}
      />

      {/* View Cart Button - Show when there are items in cart */}
      {hasItemsInCart && (
        <TouchableOpacity style={styles.viewCartButton} onPress={handleViewCart}>
          <Text style={styles.viewCartText}>View Cart</Text>
          <View style={styles.cartPriceContainer}>
            <Text style={styles.cartTotalText}>AED {cartTotal}</Text>
            <Text style={styles.cartSubtotalText}>AED {cartSubtotal}</Text>
          </View>
        </TouchableOpacity>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#F8F8F8', // Light background for the screen
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
    backgroundColor: '#FFFFFF',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  placeholder: {
    width: 30, // Same width as back button icon for balance
  },
  listContentContainer: {
    paddingHorizontal: 15,
    paddingBottom: 20, // Space for the bottom bar
  },
  itemCardContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 15,
    marginVertical: 8,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  itemImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginRight: 15,
    backgroundColor: '#EFEFEF', // Placeholder background
  },
  itemDetailsContainer: {
    flex: 1,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  itemDescription: {
    fontSize: 13,
    color: '#666',
    marginBottom: 8,
  },
  itemPrice: {
    fontSize: 15,
    fontWeight: 'bold',
    color: '#D32F2F', // Or your app's primary color
  },
  addButton: {
    backgroundColor: '#50C2C9', // Teal color from Home Screen
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 10,
  },
  viewCartButton: {
    backgroundColor: '#50C2C9', // Teal color matching the app theme
    marginHorizontal: 15,
    marginBottom: 15,
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  viewCartText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  cartPriceContainer: {
    alignItems: 'flex-end',
  },
  cartTotalText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  cartSubtotalText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#FFFFFF',
    opacity: 0.8,
  },
});