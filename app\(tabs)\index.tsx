import { Ionicons, MaterialIcons } from '@expo/vector-icons'; // For icons
import { useRouter } from 'expo-router';
import React from 'react';
import {
  Image,
  Platform,
  SafeAreaView,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View
} from 'react-native';

// Dummy data - replace with actual data from API or state management
const categories = [
  { id: '1', name: 'Cheesecake', image: require('@/assets/images/placeholder-category.svg') }, // Replace with actual images
  { id: '2', name: 'Rice Milk', image: require('@/assets/images/placeholder-category.svg') },
  { id: '3', name: 'Breakfast', image: require('@/assets/images/placeholder-category.svg') },
  { id: '4', name: 'Farghaly Juice', image: require('@/assets/images/placeholder-category.svg') },
  { id: '5', name: 'Ashtoota', image: require('@/assets/images/placeholder-category.svg') },
];

const bestSellingItems = [
  { id: 'bs1', name: 'Rice Milk Sakalans', type: 'Rice Milk', price: 16, image: require('@/assets/images/placeholder-item.svg') }, // Replace
  { id: 'bs2', name: 'Konbia', type: 'Labanita Mixat', price: 17, image: require('@/assets/images/placeholder-item.svg') },
  // Add more items
];

const offerItems = [
  { id: 'of1', name: 'Ashtoota Nutella', type: 'Ashtoota', originalPrice: 16, discountedPrice: 14, discount: '20% OFF', image: require('@/assets/images/placeholder-item.svg') }, // Replace
  { id: 'of2', name: 'Um Ali Original', type: 'Um Ali', originalPrice: 13, discountedPrice: 10, discount: '20% OFF', image: require('@/assets/images/placeholder-item.svg') },
  // Add more items
];

const farghalyMixItems = [
  { id: 'fm1', name: 'Pistachio Farghaly', price: 20, image: require('@/assets/images/placeholder-item.svg') }, // Replace
  { id: 'fm2', name: 'Caramel Farghaly', price: 22, image: require('@/assets/images/placeholder-item.svg') },
  // Add more items
];

// Reusable Product Card Component
function ProductCard({ item, isOffer = false }: { item: any, isOffer?: boolean }) {
  return (
    <View style={styles.productCard}>
      <Image source={item.image} style={styles.productImage} />
      {isOffer && item.discount && (
        <View style={styles.discountBadge}>
          <Text style={styles.discountText}>{item.discount}</Text>
        </View>
      )}
      <Text style={styles.productName}>{item.name}</Text>
      <Text style={styles.productType}>{item.type}</Text>
      <View style={styles.priceContainer}>
        {isOffer && item.originalPrice ? (
          <>
            <Text style={styles.originalPrice}>AED{item.originalPrice}</Text>
            <Text style={styles.productPrice}>AED{item.discountedPrice}</Text>
          </>
        ) : (
          <Text style={styles.productPrice}>AED{item.price}</Text>
        )}
        <TouchableOpacity style={styles.addButton}>
          <Ionicons name="add" size={20} color="white" />
        </TouchableOpacity>
      </View>
    </View>
  );
}

// Section Component for DRY principle
function ProductSection({ title, items, isOffer = false }: { title: string, items: any[], isOffer?: boolean }) {
  return (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>{title}</Text>
        <TouchableOpacity>
          <Text style={styles.seeAllText}>See all</Text>
        </TouchableOpacity>
      </View>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.productList}>
        {items.map(item => <ProductCard key={item.id} item={item} isOffer={isOffer} />)}
      </ScrollView>
    </View>
  );
}

export default function HomeScreen() {
  const router = useRouter();

  const handleCategoryPress = (categoryName: string) => {
    router.push({ pathname: '/categoryDetail', params: { categoryName } });
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle={Platform.OS === 'ios' ? 'dark-content' : 'default'} />
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Header: Location and Logo */}
        <View style={styles.header}>
          <View style={styles.locationContainer}>
            <Ionicons name="location-sharp" size={18} color="#555" />
            <Text style={styles.locationText}>Al Ain, UAE</Text>
            <MaterialIcons name="keyboard-arrow-down" size={20} color="#555" />
          </View>
          <View style={styles.logoContainer}>
            <Image source={require('@/assets/images/labanita-logo.svg')} style={styles.logo} />
            <Text style={styles.logoText}>Labanita</Text>
          </View>
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <Ionicons name="search" size={20} color="#888" style={styles.searchIcon} />
          <TextInput
            placeholder="Search Store"
            placeholderTextColor="#888"
            style={styles.searchInput}
          />
        </View>

        {/* Categories */}
        <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.categoriesContainer}>
          {categories.map(category => (
            <TouchableOpacity key={category.id} style={styles.categoryCard} onPress={() => handleCategoryPress(category.name)}>
              <Image source={category.image} style={styles.categoryImage} />
              <Text style={styles.categoryName}>{category.name}</Text>
            </TouchableOpacity>
          ))}
        </ScrollView>

        {/* End of Year Discount Banner */}
        <View style={styles.discountBanner}>
          <Image source={require('@/assets/images/discount-drinks.svg')} style={styles.discountBannerImage} />
          <View style={styles.discountBannerTextContainer}>
            <Text style={styles.discountBannerTitle}>End of year Discount</Text>
            <Text style={styles.discountBannerSubtitle}>Get Up To 40% OFF</Text>
          </View>
        </View>

        {/* Best Selling Section */}
        <ProductSection title="Best Selling" items={bestSellingItems} />

        {/* Offers Section */}
        <ProductSection title="Offers" items={offerItems} isOffer={true} />

        {/* New Arrival Banner */}
        <View style={styles.newArrivalBanner}>
            <Image source={require('@/assets/images/basabeso-arrival.svg')} style={styles.newArrivalImage} />
            <View style={styles.newArrivalTextContainer}>
                <Text style={styles.newArrivalTitle}>New Arrival</Text>
                <Text style={styles.newArrivalSubtitle}>Basabeso</Text>
            </View>
        </View>

        {/* Farghaly Mix Section */}
        <ProductSection title="Farghaly Mix" items={farghalyMixItems} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#FFFFFF', // White background for the whole screen
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingTop: Platform.OS === 'ios' ? 10 : 20,
    paddingBottom: 10,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationText: {
    marginLeft: 5,
    marginRight: 3,
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  logoContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  logo: {
    width: 100,
    height: 30,
    resizeMode: 'contain',
  },
  logoText: {
    position: 'absolute',
    fontSize: 16,
    fontWeight: 'bold',
    color: '#50C2C9',
    fontStyle: 'italic',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F0F0F0', // Light gray background for search bar
    borderRadius: 10,
    marginHorizontal: 15,
    paddingHorizontal: 10,
    paddingVertical: Platform.OS === 'ios' ? 12 : 8,
    marginBottom: 15,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 15,
    color: '#333',
  },
  categoriesContainer: {
    paddingHorizontal: 15,
    paddingBottom: 20,
  },
  categoryCard: {
    alignItems: 'center',
    marginRight: 15,
    width: 70, // Fixed width for category items
  },
  categoryImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#EFEFEF', // Placeholder background
    marginBottom: 8,
  },
  categoryName: {
    fontSize: 12,
    color: '#555',
    textAlign: 'center',
  },
  discountBanner: {
    flexDirection: 'row',
    backgroundColor: '#E0F7FA', // Light cyan background
    borderRadius: 10,
    marginHorizontal: 15,
    marginBottom: 20,
    padding: 15,
    alignItems: 'center',
  },
  discountBannerImage: {
    width: 80,
    height: 80,
    resizeMode: 'contain',
    marginRight: 15,
  },
  discountBannerTextContainer: {
    flex: 1,
  },
  discountBannerTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#00796B', // Darker cyan
  },
  discountBannerSubtitle: {
    fontSize: 14,
    color: '#D32F2F', // Red color for discount percentage
    fontWeight: '600',
  },
  sectionContainer: {
    marginBottom: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    marginBottom: 10,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  seeAllText: {
    fontSize: 14,
    color: '#50C2C9', // Teal color
    fontWeight: '500',
  },
  productList: {
    paddingHorizontal: 15,
  },
  productCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    padding: 10,
    marginRight: 15,
    width: 160, // Fixed width for product cards
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    borderWidth: 1,
    borderColor: '#F0F0F0',
  },
  productImage: {
    width: '100%',
    height: 100,
    borderRadius: 8,
    backgroundColor: '#EFEFEF', // Placeholder background
    marginBottom: 8,
    resizeMode: 'cover',
  },
  discountBadge: {
    position: 'absolute',
    top: 5,
    right: 5,
    backgroundColor: '#D32F2F', // Red badge
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  discountText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  productName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  productType: {
    fontSize: 12,
    color: '#777',
    marginBottom: 5,
  },
  priceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 'auto', // Pushes price to the bottom
  },
  productPrice: {
    fontSize: 15,
    fontWeight: 'bold',
    color: '#D32F2F', // Red price for offers, or main price
  },
  originalPrice: {
    fontSize: 12,
    color: '#999',
    textDecorationLine: 'line-through',
    marginRight: 5,
  },
  addButton: {
    backgroundColor: '#50C2C9', // Teal color
    width: 30,
    height: 30,
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
  },
  newArrivalBanner: {
    flexDirection: 'row',
    backgroundColor: '#E6FFED', // Light green background
    borderRadius: 10,
    marginHorizontal: 15,
    marginBottom: 20,
    padding: 15,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  newArrivalImage: {
    width: 100,
    height: 60,
    resizeMode: 'contain',
  },
  newArrivalTextContainer: {
    alignItems: 'flex-end',
  },
  newArrivalTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#D32F2F', // Red color
  },
  newArrivalSubtitle: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
});
