import { Feather, Ionicons } from '@expo/vector-icons'; // For icons
import { router } from 'expo-router';
import React, { useState } from 'react';
import {
  Platform,
  SafeAreaView,
  StatusBar,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';

export default function EnterNumberScreen() {
  const [phoneNumber, setPhoneNumber] = useState('');

  const handleNext = () => {
    // TODO: Implement actual phone number validation if needed beyond length check
    const fullPhoneNumber = `+971${phoneNumber}`;
    console.log('Next pressed with phone number:', fullPhoneNumber);
    router.push({ pathname: '/verifyOtp', params: { phoneNumber: fullPhoneNumber } });
  };

  const handleBack = () => {
    if (router.canGoBack()) {
      router.back();
    } else {
      // Fallback if no back history (e.g., deep link)
      router.replace('/signIn'); 
    }
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle={Platform.OS === 'ios' ? 'dark-content' : 'default'} />
      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <Ionicons name="chevron-back" size={28} color="#333333" />
        </TouchableOpacity>
      </View>

      <View style={styles.contentContainer}>
        <Text style={styles.title}>Enter your mobile number</Text>
        <Text style={styles.label}>Mobile Number</Text>
        <View style={styles.phoneInputContainer}>
          <Text style={styles.countryCodeFlag}>🇦🇪</Text>
          <Text style={styles.countryCode}>+971</Text>
          <TextInput
            style={styles.phoneInput}
            placeholder="Your phone number"
            placeholderTextColor="#B0B0B0"
            keyboardType="number-pad" // Use number-pad for direct number input
            value={phoneNumber}
            onChangeText={setPhoneNumber}
            maxLength={9} // Assuming UAE numbers are 9 digits after country code
            autoFocus={true}
          />
        </View>
      </View>

      <View style={styles.footerContainer}>
        <TouchableOpacity 
          style={[styles.nextButton, (!phoneNumber || phoneNumber.length < 9) && styles.nextButtonDisabled]}
          onPress={handleNext}
          disabled={!phoneNumber || phoneNumber.length < 9}
        >
          <Feather name="arrow-right" size={24} color="white" />
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#F7F7F7', // Light gray background similar to image
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingTop: Platform.OS === 'ios' ? 10 : 20, 
    paddingBottom: 10,
    backgroundColor: '#FFFFFF', // White header background
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  backButton: {
    padding: 5, // Easier to tap
  },
  contentContainer: {
    flex: 1, // Takes up available space, pushing footer down
    paddingHorizontal: 25,
    paddingTop: 30,
  },
  title: {
    fontSize: 26,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 30,
    // fontFamily: 'YourCustomFont-SemiBold',
  },
  label: {
    fontSize: 14,
    color: '#808080', // Medium gray
    marginBottom: 8,
    // fontFamily: 'YourCustomFont-Regular',
  },
  phoneInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    paddingHorizontal: 15,
    paddingVertical: Platform.OS === 'ios' ? 18 : 14,
    borderWidth: 1,
    borderColor: '#D0D0D0', // Slightly darker border
    marginBottom: 20,
  },
  countryCodeFlag: {
    fontSize: 24,
    marginRight: 10,
  },
  countryCode: {
    fontSize: 18,
    color: '#333333',
    marginRight: 8,
    fontWeight: '500',
    // fontFamily: 'YourCustomFont-Medium',
  },
  phoneInput: {
    flex: 1,
    fontSize: 18,
    color: '#333333',
    // fontFamily: 'YourCustomFont-Regular',
  },
  footerContainer: {
    padding: 25,
    alignItems: 'flex-end', // Align button to the right
    backgroundColor: '#F7F7F7', // Match safeArea background
  },
  nextButton: {
    backgroundColor: '#50C2C9', // Teal color from previous screens
    width: 60,
    height: 60,
    borderRadius: 30, // Circular button
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.23,
    shadowRadius: 2.62,
    elevation: 4,
  },
  nextButtonDisabled: {
    backgroundColor: '#A0D8DC', // Lighter, disabled state color
  },
});